import asyncio
import requests

async def get_dollar_price():
    # Using a free API that doesn't require authentication
    url = "https://api.exchangerate-api.com/v4/latest/USD"
    try:
        response = requests.get(url, timeout=10)
        data = response.json()
        price_irr = data['rates']['IRR']
        price_toman = int(price_irr / 10)
        return f"{price_toman:,}"
    except Exception as e:
        print(f"Error getting dollar price: {e}")
        return None

async def main():
    print("Testing dollar price API...")
    price = await get_dollar_price()
    if price:
        print(f"قیمت روز دلار: {price} تومان")
    else:
        print("خطا در دریافت قیمت دلار.")

if __name__ == '__main__':
    asyncio.run(main())
