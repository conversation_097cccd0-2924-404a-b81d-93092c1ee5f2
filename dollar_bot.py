import requests
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ApplicationBuilder, CommandHandler, ContextTypes, CallbackQueryHandler

TELEGRAM_TOKEN = '**********************************************'

async def get_dollar_price():
    url = "https://api.exchangerate.host/latest?base=USD&symbols=IRR"
    try:
        response = requests.get(url, timeout=10)
        data = response.json()
        price_irr = data['rates']['IRR']
        price_toman = int(price_irr / 10)
        return f"{price_toman:,}"
    except Exception:
        return None

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    keyboard = [
        [InlineKeyboardButton('💵 دریافت قیمت دلار', callback_data='get_dollar')]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)
    await update.message.reply_text(
        "سلام! برای دریافت قیمت دلار روی دکمه زیر بزنید یا دستور /dollar را ارسال کنید.",
        reply_markup=reply_markup
    )

async def dollar(update: Update, context: ContextTypes.DEFAULT_TYPE):
    price = await get_dollar_price()
    if price:
        await update.message.reply_text(f"قیمت روز دلار: {price} تومان")
    else:
        await update.message.reply_text("خطا در دریافت قیمت دلار.")

async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    query = update.callback_query
    await query.answer()
    if query.data == 'get_dollar':
        price = await get_dollar_price()
        if price:
            await query.edit_message_text(f"قیمت روز دلار: {price} تومان")
        else:
            await query.edit_message_text("خطا در دریافت قیمت دلار.")

if __name__ == '__main__':
    app = ApplicationBuilder().token(TELEGRAM_TOKEN).build()
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("dollar", dollar))
    app.add_handler(CallbackQueryHandler(button_handler))
    app.run_polling() 